'use client';

import { useRef, useState, useCallback, useEffect } from 'react';
import * as THREE from 'three';

// Import utilities
import HistoryManager from './utils/historyManager';
import { AIIdentifiedObject, AIServiceType } from './utils/aiService';
import { TransformMode } from './utils/objectTransformations';
import { SceneSetup } from './utils/sceneSetup';

// Import database integration
import { EnhancedAIService } from '@/lib/enhancedAIService';
import { useModelDatabase } from '@/hooks/useModelDatabase';

// Import components
import ThreeViewer from './ThreeViewer';
import HelpPanel from './components/HelpPanel';
import ControlPanel from './components/ControlPanel';
import LoadingOverlay from './components/LoadingOverlay';
import AIPromptPanel from './components/AIPromptPanel';
import ModelHistoryPanel from './ModelHistoryPanel';

interface ThreeViewerContainerProps {
  modelData: { url: string; fileType: string; fileName: string } | null;
  key?: string; // Add a key prop to force component remount when model is cleared
}

export default function ThreeViewerContainer({
  modelData,
}: ThreeViewerContainerProps) {
  const { fileName, url } = modelData || {};

  // AI service state
  const [aiServiceType, setAiServiceType] = useState<AIServiceType>('gemini');

  // Refs
  const sceneSetupRef = useRef<SceneSetup | null>(null);
  const modelRef = useRef<THREE.Object3D | null>(null);
  const historyManagerRef = useRef<HistoryManager>(new HistoryManager());
  const { current: enhancedAIService } = useRef<EnhancedAIService>(
    new EnhancedAIService(aiServiceType),
  );

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedObjects, setSelectedObjects] = useState<THREE.Object3D[]>([]);
  const [transformMode, setTransformMode] =
    useState<TransformMode>('translate');
  const [transformSpeed, setTransformSpeed] = useState(0.1);
  const [showHelp, setShowHelp] = useState(false);

  // AI and history state
  const [showAIPanel, setShowAIPanel] = useState(false);
  const [isProcessingAI, setIsProcessingAI] = useState(false);
  const [promptHistory, setPromptHistory] = useState<
    {
      prompt: string;
      response: string;
      timestamp: number;
    }[]
  >([]);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  // Model analysis state
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisError, setAnalysisError] = useState(false);
  const [analysisNotification, setAnalysisNotification] = useState<
    string | null
  >(null);

  // Database integration state
  const [showHistory, setShowHistory] = useState(false);
  const [databaseInitialized, setDatabaseInitialized] = useState(false);

  // Database hooks
  const { createOrGetModel, getModelState } = useModelDatabase();

  const registeredObjects = enhancedAIService.getRegisteredObjects();
  console.log('registeredObjects', registeredObjects);

  // Save current model state to database
  // const saveCurrentModelState = useCallback(async () => {
  //   if (!sceneSetupRef.current || !fileName) return;

  //   try {
  //     const objectStates: Array<{
  //       objectId: string;
  //       objectName?: string;
  //       position: { x: number; y: number; z: number };
  //       rotation: { x: number; y: number; z: number };
  //       scale: { x: number; y: number; z: number };
  //       visible: boolean;
  //       selected: boolean;
  //       properties?: Record<string, unknown>;
  //     }> = [];

  //     sceneSetupRef.current.scene.traverse(object => {
  //       if (object instanceof THREE.Mesh && object.uuid) {
  //         objectStates.push({
  //           objectId: object.uuid,
  //           objectName: object.name || undefined,
  //           position: {
  //             x: object.position.x,
  //             y: object.position.y,
  //             z: object.position.z,
  //           },
  //           rotation: {
  //             x: object.rotation.x,
  //             y: object.rotation.y,
  //             z: object.rotation.z,
  //           },
  //           scale: {
  //             x: object.scale.x,
  //             y: object.scale.y,
  //             z: object.scale.z,
  //           },
  //           visible: object.visible,
  //           selected: selectedObjects.includes(object),
  //           properties: {
  //             materialType: object.material?.type,
  //             geometryType: object.geometry?.type,
  //           },
  //         });
  //       }
  //     });

  //     await saveModelState(fileName, objectStates);
  //     console.log(`Saved state for ${objectStates.length} objects`);
  //   } catch (error) {
  //     console.error('Error saving model state:', error);
  //   }
  // }, [fileName, saveModelState, selectedObjects]);

  // Load model state from database
  const loadModelState = useCallback(
    async (fileName: string) => {
      if (!sceneSetupRef.current) return;

      try {
        const objectStates = await getModelState(fileName);

        if (objectStates.length === 0) {
          console.log('No saved state found for current model');
          return;
        }

        // Create a map of scene objects by UUID
        const sceneObjects = new Map<string, THREE.Mesh>();
        sceneSetupRef.current.scene.traverse(object => {
          if (object instanceof THREE.Mesh && object.uuid) {
            sceneObjects.set(object.uuid, object);
          }
        });

        // Apply saved state to objects
        let appliedCount = 0;
        for (const state of objectStates) {
          const object = sceneObjects.get(state.objectId);
          if (object) {
            object.position.set(
              state.position.x,
              state.position.y,
              state.position.z,
            );
            object.rotation.set(
              state.rotation.x,
              state.rotation.y,
              state.rotation.z,
            );
            object.scale.set(state.scale.x, state.scale.y, state.scale.z);
            object.visible = state.visible;

            if (state.objectName) {
              object.name = state.objectName;
            }

            appliedCount++;
          }
        }

        console.log(`Applied saved state to ${appliedCount} objects`);
      } catch (error) {
        console.error('Error loading model state:', error);
      }
    },
    [getModelState],
  );

  // Initialize database integration when model and filename are available
  const initializeDatabaseIntegration = useCallback(
    async (fileName: string, url: string) => {
      try {
        console.log(`Initializing database integration for: ${fileName}`);

        // Create or get model in database
        const model = await createOrGetModel(fileName, url);
        console.log('model', model);

        if (!model) {
          console.log('No model created or found');
          return;
        }

        // Set current model with database integration
        enhancedAIService.setCurrentModel(model);

        // If model has analysis data, populate registeredObjects
        if (model.analysis) {
          try {
            const analysisResult = JSON.parse(model.analysis.result);
            if (
              analysisResult.objects &&
              Array.isArray(analysisResult.objects)
            ) {
              console.log(
                `Loading ${analysisResult.objects.length} objects from saved analysis`,
              );

              // Clear existing objects and populate with saved analysis
              enhancedAIService.clearObjects();

              // Register objects from saved analysis
              analysisResult.objects.forEach((obj: AIIdentifiedObject) => {
                enhancedAIService.registerObject(obj);
              });

              console.log(
                `Populated registeredObjects with ${analysisResult.objects.length} objects from database`,
              );
            }
          } catch (parseError) {
            console.error('Error parsing saved analysis data:', parseError);
          }
        }

        // Try to load saved model state
        await loadModelState(fileName);

        setDatabaseInitialized(true);
        console.log(`Database integration initialized for: ${fileName}`);
      } catch (error) {
        console.error('Error initializing database integration:', error);
      }
    },
    [createOrGetModel, enhancedAIService, loadModelState],
  );

  // Handle AI prompt submission
  const handleSubmitPrompt = useCallback(
    async (prompt: string) => {
      if (!sceneSetupRef.current || isProcessingAI) return;

      setIsProcessingAI(true);

      try {
        // Save current state before making changes
        historyManagerRef.current.addState(
          sceneSetupRef.current.scene,
          `Before AI prompt: "${prompt}"`,
        );

        // Process prompt using EnhancedAIService
        const response = await enhancedAIService.processPromptUniversal(
          prompt,
          sceneSetupRef.current.scene,
          'modification',
        );

        // Apply the changes to the scene
        enhancedAIService.applyChanges(
          sceneSetupRef.current.scene,
          response.changes,
        );

        // Save the new state after changes
        historyManagerRef.current.addState(
          sceneSetupRef.current.scene,
          `After AI prompt: "${prompt}"`,
        );

        // Update undo/redo availability
        setCanUndo(historyManagerRef.current.canUndo());
        setCanRedo(historyManagerRef.current.canRedo());

        // Add to prompt history
        setPromptHistory(prev => [
          ...prev,
          {
            prompt,
            response: response.explanation,
            timestamp: Date.now(),
          },
        ]);
      } catch (error) {
        console.error('Error processing AI prompt:', error);

        // Add error to prompt history
        setPromptHistory(prev => [
          ...prev,
          {
            prompt,
            response: `Error: ${
              error instanceof Error
                ? error.message
                : 'Failed to process prompt'
            }`,
            timestamp: Date.now(),
          },
        ]);
      } finally {
        setIsProcessingAI(false);
      }
    },
    [isProcessingAI, enhancedAIService],
  );

  // Handle undo
  const handleUndo = useCallback(() => {
    if (!sceneSetupRef.current || !historyManagerRef.current.canUndo()) return;

    historyManagerRef.current.undo(sceneSetupRef.current.scene);

    // Update undo/redo availability
    setCanUndo(historyManagerRef.current.canUndo());
    setCanRedo(historyManagerRef.current.canRedo());
  }, []);

  // Handle redo
  const handleRedo = useCallback(() => {
    if (!sceneSetupRef.current || !historyManagerRef.current.canRedo()) return;

    historyManagerRef.current.redo(sceneSetupRef.current.scene);

    // Update undo/redo availability
    setCanUndo(historyManagerRef.current.canUndo());
    setCanRedo(historyManagerRef.current.canRedo());
  }, []);

  // Toggle AI panel
  const handleToggleAIPanel = useCallback(() => {
    setShowAIPanel(prev => !prev);
  }, []);

  // Handle model analysis
  const handleAnalyzeModel = useCallback(async () => {
    if (!sceneSetupRef.current || !modelRef.current || isAnalyzing) return;

    setIsAnalyzing(true);
    setAnalysisError(false); // Reset error state

    try {
      console.log(`Starting model analysis with ${aiServiceType} service...`);

      // Clear any existing object mappings
      enhancedAIService.clearObjects();

      // Use universal analysis method
      const analysisId = await enhancedAIService.analyzeSceneWithDatabase(
        sceneSetupRef.current.scene,
        false,
      );

      // Show notification
      const message = analysisId
        ? `Model analysis complete and saved (ID: ${analysisId})! AI assistant is now ready.`
        : 'Model analysis complete! AI assistant is now ready.';

      setAnalysisNotification(message);

      // Clear notification after 5 seconds
      setTimeout(() => {
        setAnalysisNotification(null);
      }, 5000);

      console.log('Model analysis complete');
    } catch (error) {
      console.error('Error analyzing model:', error);

      // Set error state to true to show fallback button
      setAnalysisError(true);

      // Show error notification with more details
      let errorMessage = 'Error analyzing model. Please try again.';

      if (error instanceof Error) {
        errorMessage = `Error analyzing model: ${error.message}`;

        // If it's a 400 error, add more helpful information
        if (error.message.includes('400')) {
          errorMessage +=
            '. This may be due to an invalid request format or content limitations.';
        }
        // If it's a 401/403 error, suggest checking API key
        else if (
          error.message.includes('401') ||
          error.message.includes('403')
        ) {
          errorMessage +=
            '. Please check that your API key is valid and has the necessary permissions.';
        }
        // If it's a 429 error, suggest rate limiting
        else if (error.message.includes('429')) {
          errorMessage +=
            '. The API rate limit has been exceeded. Please try again later.';
        }
      }

      setAnalysisNotification(errorMessage);

      // Log more details for debugging
      console.log('Analysis error details:', {
        error,
        errorMessage,
        aiService: aiServiceType,
        modelRef: modelRef.current ? 'Model exists' : 'No model',
        sceneRef: sceneSetupRef.current ? 'Scene exists' : 'No scene',
      });

      // Clear notification after 12 seconds (longer for error messages)
      setTimeout(() => {
        setAnalysisNotification(null);
      }, 12000);
    } finally {
      setIsAnalyzing(false);
    }
  }, [isAnalyzing, aiServiceType, enhancedAIService]);

  // Handle basic model analysis (fallback)
  const handleAnalyzeModelBasic = useCallback(async () => {
    if (!sceneSetupRef.current || !modelRef.current || isAnalyzing) return;

    setIsAnalyzing(true);
    setAnalysisError(false);

    try {
      console.log('Starting basic model analysis...');

      // Clear any existing object mappings
      enhancedAIService.clearObjects();

      // Perform the analysis with useFallback=true
      await enhancedAIService.analyzeScene(sceneSetupRef.current.scene, true);

      // Show notification
      setAnalysisNotification(
        'Basic model analysis complete. AI assistant is now ready with limited functionality.',
      );

      console.log('Basic model analysis complete');
    } catch (error) {
      console.error('Error in basic model analysis:', error);

      // Show error notification
      setAnalysisNotification(
        'Error in basic analysis. Please check the console for details.',
      );
    } finally {
      setIsAnalyzing(false);
      // Clear notification after 5 seconds
      setTimeout(() => {
        setAnalysisNotification(null);
      }, 5000);
    }
  }, [enhancedAIService, isAnalyzing]);

  // Reset model analyzed state when model changes
  useEffect(() => {
    setAnalysisError(false);
    setDatabaseInitialized(false);
  }, [fileName]);

  // Initialize database integration when model and filename are available
  useEffect(() => {
    if (!fileName || !url || databaseInitialized) {
      return;
    }

    initializeDatabaseIntegration(fileName, url);
  }, [fileName, url, databaseInitialized, initializeDatabaseIntegration]);

  return (
    <>
      <ThreeViewer
        modelData={modelData}
        setIsLoading={setIsLoading}
        setError={setError}
        setSelectedObjects={setSelectedObjects}
        selectedObjects={selectedObjects}
        transformMode={transformMode}
        setTransformMode={setTransformMode}
        transformSpeed={transformSpeed}
        setTransformSpeed={setTransformSpeed}
        sceneSetupRef={sceneSetupRef}
        modelRef={modelRef}
        historyManagerRef={historyManagerRef}
        setCanUndo={setCanUndo}
        setCanRedo={setCanRedo}
      />

      {/* Analysis notification */}
      {analysisNotification && (
        <div
          className={`fixed top-4 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded shadow-lg z-50 animate-fade-in ${
            analysisNotification.includes('Error')
              ? 'bg-red-100 border border-red-400 text-red-700'
              : 'bg-green-100 border border-green-400 text-green-700'
          }`}
        >
          {analysisNotification}
        </div>
      )}

      <LoadingOverlay isLoading={isLoading} error={error} />

      {showHelp && (
        <HelpPanel
          transformMode={transformMode}
          onClose={() => setShowHelp(false)}
        />
      )}

      {showAIPanel && (
        <AIPromptPanel
          onSubmitPrompt={handleSubmitPrompt}
          promptHistory={promptHistory}
          isProcessing={isProcessingAI}
        />
      )}

      {/* Model History Panel */}
      {showHistory && (
        <ModelHistoryPanel
          fileName={fileName}
          isVisible={showHistory}
          onClose={() => setShowHistory(false)}
        />
      )}

      {/* Model History Button */}
      {fileName && databaseInitialized && (
        <button
          onClick={() => setShowHistory(true)}
          className="fixed top-4 right-4 z-10 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 shadow-lg"
          title="View model history"
        >
          📊 History
        </button>
      )}

      <ControlPanel
        selectedObjects={selectedObjects}
        transformMode={transformMode}
        transformSpeed={transformSpeed}
        toggleShowHelp={() => setShowHelp(prev => !prev)}
        canUndo={canUndo}
        canRedo={canRedo}
        onUndo={handleUndo}
        onRedo={handleRedo}
        showAIPanel={showAIPanel}
        onToggleAIPanel={handleToggleAIPanel}
        onClearSelection={() => {
          setSelectedObjects([]);
        }}
        modelLoaded={!!modelRef.current}
        modelAnalyzed={registeredObjects.length > 0}
        isAnalyzing={isAnalyzing}
        onAnalyzeModel={handleAnalyzeModel}
        onAnalyzeModelBasic={handleAnalyzeModelBasic}
        analysisError={analysisError}
        aiServiceType={aiServiceType}
        onChangeAIService={setAiServiceType}
      />
    </>
  );
}
