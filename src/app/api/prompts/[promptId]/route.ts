import { NextRequest, NextResponse } from 'next/server';
import { ModelService } from '@/lib/modelService';

// PUT /api/prompts/[promptId] - обновить промпт с ответом AI
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ promptId: string }> },
) {
  try {
    const { promptId } = await params;
    const body = await request.json();
    const { aiResponse, status } = body;

    if (!aiResponse) {
      return NextResponse.json(
        { error: 'aiResponse is required' },
        { status: 400 },
      );
    }

    const prompt = await ModelService.updatePromptWithResponse(
      promptId,
      aiResponse,
      status || 'completed',
    );

    return NextResponse.json({
      ...prompt,
      metadata: prompt.metadata ? JSON.parse(prompt.metadata) : null,
    });
  } catch (error) {
    console.error('Error updating prompt:', error);
    return NextResponse.json(
      { error: 'Failed to update prompt' },
      { status: 500 },
    );
  }
}

// PATCH /api/prompts/[promptId] - обновить промпт с ответом AI (alias for PUT)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ promptId: string }> },
) {
  return PUT(request, { params });
}
